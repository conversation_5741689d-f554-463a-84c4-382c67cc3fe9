import { Recipe, MealType, InstructionType } from '@/components/types';
import { firestoreRepository, PaginationResult } from '@/repositories/firestoreRepository';
import { UnsplashService } from '@/services/UnsplashService';
import { FirebaseFirestoreTypes } from '@react-native-firebase/firestore';

// Type alias for react-native-firebase
type QueryDocumentSnapshot = FirebaseFirestoreTypes.QueryDocumentSnapshot<FirebaseFirestoreTypes.DocumentData>;

/**
 * Service for fetching recipes from various sources
 */
export class RecipeService {
  /**
   * Fetch recipes from Firestore for a specific user with pagination
   *
   * @param userId The user's UID
   * @param pageSize Number of recipes to fetch per page
   * @param lastVisible Last visible document for pagination cursor
   * @returns Paginated result with recipes and pagination metadata
   */
  static async fetchRecipesFromFirestorePaginated(
    userId: string,
    pageSize: number = 10,
    lastVisible?: QueryDocumentSnapshot | null
  ): Promise<PaginationResult<Recipe>> {
    try {
      console.log('Fetching paginated recipes from Firestore for user:', userId, 'pageSize:', pageSize);

      // Fetch paginated recipes from Firestore subcollection
      const paginationResult = await firestoreRepository.getPaginatedRecipes(userId, pageSize, lastVisible);

      if (!paginationResult.data || paginationResult.data.length === 0) {
        console.log('No recipes found in Firestore for user:', userId);
        return {
          data: [],
          lastVisible: null,
          hasMore: false,
        };
      }

      console.log(`Found ${paginationResult.data.length} recipes in Firestore`);

      // Transform Firestore recipes to match our Recipe interface
      const transformedRecipes: Recipe[] = await Promise.all(
        paginationResult.data.map(async (recipe: any) => {
          // Generate image URL if not present or if imageQuery is available
          let imageUrl = recipe.imageUrl;
          if (!imageUrl && recipe.imageQuery) {
            try {
              const imageResponse = await UnsplashService.getImageUrl(recipe.imageQuery);
              if (imageResponse.success && imageResponse.imageUrl) {
                imageUrl = imageResponse.imageUrl;
              } else {
                console.warn('Failed to fetch image for recipe:', recipe.title, imageResponse.error);
                imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
              }
            } catch (error) {
              console.warn('Failed to fetch image for recipe:', recipe.title, error);
              imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
            }
          } else if (!imageUrl) {
            imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
          }

          // Transform instructions from Python format to TypeScript format
          let instructions = {
            [InstructionType.HIGH_LEVEL]: 'Instructions not available',
            [InstructionType.DETAILED]: 'Instructions not available',
            [InstructionType.TEACH_MODE]: 'Instructions not available',
          };

          if (recipe.instructions) {
            instructions = {
              [InstructionType.HIGH_LEVEL]:
                recipe.instructions[InstructionType.HIGH_LEVEL] || 'Instructions not available',
              [InstructionType.DETAILED]: recipe.instructions[InstructionType.DETAILED] || 'Instructions not available',
              [InstructionType.TEACH_MODE]:
                recipe.instructions[InstructionType.TEACH_MODE] || 'Instructions not available',
            };
          }

          // Ensure the recipe has all required fields with defaults
          return {
            id: recipe.id || `firestore-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            title: recipe.title || 'Untitled Recipe',
            timeInMinutes: recipe.timeInMinutes || 30,
            calories: recipe.calories || 400,
            imageUrl,
            compatibleDiets: recipe.compatibleDiets || [],
            ingredients: recipe.ingredients || [],
            instructions,
            mealType: recipe.mealType || MealType.LUNCH,
          };
        })
      );

      return {
        data: transformedRecipes,
        lastVisible: paginationResult.lastVisible,
        hasMore: paginationResult.hasMore,
      };
    } catch (error) {
      console.error('Error fetching paginated recipes from Firestore:', error);
      return {
        data: [],
        lastVisible: null,
        hasMore: false,
      };
    }
  }
}
