import { Timestamp } from '@react-native-firebase/firestore';
import { auth } from '@/firebase/firebaseConfig';
import { firestoreRepository, FirestoreCollections } from '@/repositories/firestoreRepository';
import { Recipe } from '@/components/types';

export interface SavedRecipeReference {
  savedAt: Timestamp;
}

/**
 * Service for managing saved recipes
 */
export class SavedRecipesService {
  /**
   * Save a recipe to favorites
   * Saves the full recipe to the savedRecipes collection and creates a reference in the user's subcollection
   */
  static async saveRecipe(recipe: Recipe): Promise<void> {
    try {
      const user = auth().currentUser;
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Save the full recipe to the savedRecipes collection using the recipe ID
      await firestoreRepository.addOrReplaceDocument(FirestoreCollections.SAVED_RECIPES, recipe.id, recipe);

      // Create a reference in the user's savedRecipes subcollection
      const savedRecipeRef: SavedRecipeReference = {
        savedAt: Timestamp.now(),
      };

      await firestoreRepository.addSubcollectionDocument(
        FirestoreCollections.USERS,
        user.uid,
        'savedRecipes',
        recipe.id,
        savedRecipeRef
      );

      console.log('Recipe saved successfully:', recipe.id);
    } catch (error) {
      console.error('Error saving recipe:', error);
      throw error;
    }
  }

  /**
   * Remove a recipe from favorites
   * Removes the reference from the user's subcollection (keeps the recipe in savedRecipes collection for other users)
   */
  static async unsaveRecipe(recipeId: string): Promise<void> {
    try {
      const user = auth().currentUser;
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Remove the reference from the user's savedRecipes subcollection
      await firestoreRepository.deleteSubcollectionDocument(
        FirestoreCollections.USERS,
        user.uid,
        'savedRecipes',
        recipeId
      );

      console.log('Recipe unsaved successfully:', recipeId);
    } catch (error) {
      console.error('Error unsaving recipe:', error);
      throw error;
    }
  }

  /**
   * Get all saved recipe IDs for the current user
   */
  static async getSavedRecipeIds(): Promise<string[]> {
    try {
      const user = auth().currentUser;
      if (!user) {
        console.log('Cannot get saved recipes: User not authenticated');
        return [];
      }

      const savedRecipeRefs = await firestoreRepository.getSubcollection(
        FirestoreCollections.USERS,
        user.uid,
        'savedRecipes'
      );

      return savedRecipeRefs.map((ref) => ref.id);
    } catch (error) {
      console.error('Error getting saved recipe IDs:', error);
      return [];
    }
  }

  /**
   * Get all saved recipes for the current user
   */
  static async getSavedRecipes(): Promise<Recipe[]> {
    try {
      const user = auth().currentUser;
      if (!user) {
        console.log('Cannot get saved recipes: User not authenticated');
        return [];
      }

      // Get the saved recipe references from the user's subcollection (only fetch once)
      const savedRecipeRefs = await firestoreRepository.getSubcollection(
        FirestoreCollections.USERS,
        user.uid,
        'savedRecipes'
      );

      if (savedRecipeRefs.length === 0) {
        return [];
      }

      // Fetch the full recipe details from the savedRecipes collection in parallel
      const recipePromises = savedRecipeRefs.map((recipeRef) =>
        firestoreRepository.getDocument(FirestoreCollections.SAVED_RECIPES, recipeRef.id)
      );

      const recipeDocs = await Promise.all(recipePromises);

      // Filter out null results and convert to Recipe objects
      const recipes: Recipe[] = recipeDocs.filter((doc) => doc !== null).map((doc) => doc as Recipe);

      // Sort by savedAt timestamp (most recent first) using the refs we already have
      const sortedRecipes = recipes.sort((a, b) => {
        const aRef = savedRecipeRefs.find((ref) => ref.id === a.id);
        const bRef = savedRecipeRefs.find((ref) => ref.id === b.id);

        if (!aRef || !bRef) return 0;

        const aTime = aRef.savedAt?.toMillis() || 0;
        const bTime = bRef.savedAt?.toMillis() || 0;

        return bTime - aTime; // Most recent first
      });

      return sortedRecipes;
    } catch (error) {
      console.error('Error getting saved recipes:', error);
      return [];
    }
  }

  /**
   * Check if a recipe is saved by the current user
   */
  static async isRecipeSaved(recipeId: string): Promise<boolean> {
    try {
      const savedRecipeIds = await this.getSavedRecipeIds();
      return savedRecipeIds.includes(recipeId);
    } catch (error) {
      console.error('Error checking if recipe is saved:', error);
      return false;
    }
  }
}
