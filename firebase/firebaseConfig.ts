import { getApp } from '@react-native-firebase/app';
import { getAuth } from '@react-native-firebase/auth';
import { getFirestore, collection } from '@react-native-firebase/firestore';

// Firebase is automatically initialized with react-native-firebase
// using the google-services.json and GoogleService-Info.plist files

// Get Firebase app instance
export const app = getApp();

// Export auth instance using modular SDK
export const auth = getAuth(app);

// Export firestore instance using modular SDK
export const db = getFirestore(app);

// Export collection references for backward compatibility
export const dietPreferenceCollection = collection(db, 'dietPreferences');
export const conversationCollection = collection(db, 'conversations');
export const inventoryCollection = collection(db, 'inventory');
export const groceryListCollection = collection(db, 'groceryList');
export const generatedRecipesCollection = collection(db, 'generatedRecipes');
export const usersCollection = collection(db, 'users');
export const savedReipesCollection = collection(db, 'savedRecipes');
