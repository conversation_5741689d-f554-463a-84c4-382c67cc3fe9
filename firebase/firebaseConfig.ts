import firebase from '@react-native-firebase/app';
import auth from '@react-native-firebase/auth';
import firestore from '@react-native-firebase/firestore';

// Firebase is automatically initialized with react-native-firebase
// using the google-services.json and GoogleService-Info.plist files

// Get Firebase app instance - use the default app
export const app = firebase.app();

// Export auth instance directly
export { auth };

// Export firestore instance directly
export const db = firestore();

// Export collection references for backward compatibility
export const dietPreferenceCollection = db.collection('dietPreferences');
export const conversationCollection = db.collection('conversations');
export const inventoryCollection = db.collection('inventory');
export const groceryListCollection = db.collection('groceryList');
export const generatedRecipesCollection = db.collection('generatedRecipes');
export const usersCollection = db.collection('users');
export const savedReipesCollection = db.collection('savedRecipes');
