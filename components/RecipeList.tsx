import React from 'react';
import { View, ActivityIndicator } from 'react-native';
import { Text } from 'react-native-paper';
import { useColorScheme } from 'react-native';
import { getThemeColors } from '@/styles/Theme';
import createRecipeStyles from '@/styles/RecipeStyles';
import LoadingAnimation from '@/components/LoadingAnimation';
import RecipeCard from '@/components/RecipeCard';
import { Recipe, InstructionType } from '@/components/types';

interface RecipeListProps {
  recipes: Recipe[];
  isLoading: boolean;
  refreshing: boolean;
  loadingMore: boolean;
  error: string | null;
  canLoadMore: boolean;
  expandedRecipeIds: Set<string>;
  favorites: Set<string>;
  generatingDetails: Set<string>;
  servings: number;
  instructionType: InstructionType;
  instructionTypes: InstructionType[];
  loadingRecipeDetails: string | null;
  lastResponseId: string | null;
  isPollingForFirstTimeRecipes?: boolean;
  onToggleFavorite: (recipeId: string, e: any, recipe?: Recipe, lastResponseId?: string | null) => void;
  onToggleExpanded: (recipeId: string) => void;
  onSelectInstructionType: (type: InstructionType) => void;
  onChangeServings: (servings: number) => void;
}

const RecipeList: React.FC<RecipeListProps> = ({
  recipes,
  isLoading,
  refreshing,
  loadingMore,
  error,
  canLoadMore,
  expandedRecipeIds,
  favorites,
  generatingDetails,
  servings,
  instructionType,
  instructionTypes,
  loadingRecipeDetails,
  lastResponseId,
  isPollingForFirstTimeRecipes = false,
  onToggleFavorite,
  onToggleExpanded,
  onSelectInstructionType,
  onChangeServings,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const colors = getThemeColors(colorScheme as 'light' | 'dark');
  const recipeStyles = createRecipeStyles(colorScheme as 'light' | 'dark');

  const renderRecipeCard = (recipe: Recipe) => {
    const isExpanded = expandedRecipeIds.has(recipe.id);
    const isFavorite = favorites.has(recipe.id);
    const isLoadingDetails = loadingRecipeDetails === recipe.id;
    const isGeneratingDetails = generatingDetails.has(recipe.id);

    return (
      <View key={recipe.id} style={recipeStyles.recipeCardContainer}>
        <RecipeCard
          recipe={recipe}
          isExpanded={isExpanded}
          isFavorite={isFavorite}
          servings={servings}
          instructionType={instructionType}
          instructionTypes={instructionTypes}
          isLoadingDetails={isLoadingDetails}
          isGeneratingDetails={isGeneratingDetails}
          onToggleFavorite={(e: any) => onToggleFavorite(recipe.id, e, recipe, lastResponseId)}
          onToggleExpanded={() => onToggleExpanded(recipe.id)}
          onSelectInstructionType={onSelectInstructionType}
          onChangeServings={onChangeServings}
        />
      </View>
    );
  };

  // Error state
  if (error) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
        <Text style={{ color: 'red', textAlign: 'center' }}>{error}</Text>
      </View>
    );
  }

  // Loading state
  if (isLoading || refreshing) {
    return (
      <View style={recipeStyles.loadingContainer}>
        <LoadingAnimation
          source={require('../assets/images/gifs/fry-pan.gif')}
          message='Loading delicious recipes for you...'
        />
      </View>
    );
  }

  // Special loading state for first-time recipe polling
  if (isPollingForFirstTimeRecipes && recipes.length === 0) {
    return (
      <View style={recipeStyles.loadingContainer}>
        <LoadingAnimation
          source={require('../assets/images/gifs/fry-pan.gif')}
          message='Crafting your personalized recipes for the first time. They will be ready within 3 minutes 👩‍🍳'
        />
      </View>
    );
  }

  return (
    <>
      {/* Recipe Cards */}
      {recipes.map(renderRecipeCard)}

      {/* Load More Indicator */}
      {!isLoading && !refreshing && !error && (canLoadMore || loadingMore) && (
        <View style={{ padding: 20, alignItems: 'center', minHeight: 60 }}>
          {loadingMore ? (
            <ActivityIndicator size='small' color={colors.accent} />
          ) : (
            <Text style={{ color: colors.text }}>Scroll for more recipes</Text>
          )}
        </View>
      )}
    </>
  );
};

export default RecipeList;
